<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Luxe Fashion - Modern Clothing Store</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#4B0082',
                        'secondary': '#D8BFD8',
                        'accent': '#9370DB',
                        'purple-light': '#E6E6FA',
                        'purple-dark': '#301934'
                    },
                    fontFamily: {
                        'display': ['Inter', 'system-ui', 'sans-serif'],
                        'body': ['Inter', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'fade-in-up': 'fadeInUp 0.6s ease-out',
                        'pulse-slow': 'pulse 3s infinite',
                        'bounce-slow': 'bounce 2s infinite'
                    },
                    backdropBlur: {
                        'xs': '2px',
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-white text-gray-800 font-body">
    <!-- Announcement Bar -->
    <div class="bg-gradient-to-r from-primary to-accent text-white text-center py-2 text-sm font-medium tracking-wide">
        <div class="flex items-center justify-center space-x-2">
            <i class="fas fa-shipping-fast animate-bounce-slow"></i>
            <span>FREE SHIPPING on orders over $75 | Use code: FREESHIP75</span>
            <button class="ml-4 text-white hover:text-secondary transition-colors duration-300" id="close-announcement">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="bg-white/90 backdrop-blur-xl shadow-2xl fixed top-4 left-4 right-4 z-50 transition-all duration-300 rounded-2xl border border-white/20" id="navbar">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <h1 class="text-2xl font-bold text-primary font-display tracking-tight">Luxe Fashion</h1>
                </div>

                <!-- Desktop Menu -->
                <div class="hidden lg:block">
                    <div class="ml-10 flex items-baseline space-x-8">
                        <a href="#" class="text-gray-700 hover:text-primary transition duration-300 font-medium relative group">
                            Home
                            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                        </a>
                        <div class="relative group">
                            <a href="#" class="text-gray-700 hover:text-primary transition duration-300 font-medium flex items-center">
                                Women <i class="fas fa-chevron-down ml-1 text-xs"></i>
                            </a>
                            <div class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                                <div class="py-2">
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-purple-light hover:text-primary">Dresses</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-purple-light hover:text-primary">Tops</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-purple-light hover:text-primary">Bottoms</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-purple-light hover:text-primary">Outerwear</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <a href="#" class="text-gray-700 hover:text-primary transition duration-300 font-medium flex items-center">
                                Men <i class="fas fa-chevron-down ml-1 text-xs"></i>
                            </a>
                            <div class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                                <div class="py-2">
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-purple-light hover:text-primary">Shirts</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-purple-light hover:text-primary">Pants</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-purple-light hover:text-primary">Jackets</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-purple-light hover:text-primary">Shoes</a>
                                </div>
                            </div>
                        </div>
                        <a href="#" class="text-gray-700 hover:text-primary transition duration-300 font-medium relative group">
                            Accessories
                            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                        </a>
                        <a href="#" class="text-red-500 hover:text-red-600 transition duration-300 font-medium relative group">
                            Sale
                            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-red-500 transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="hidden md:flex flex-1 max-w-md mx-8">
                    <div class="relative w-full">
                        <input type="text" placeholder="Search for products..."
                               class="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
                               id="search-input">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        <div id="search-suggestions" class="absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-xl hidden z-50">
                            <div class="py-2">
                                <div class="px-4 py-2 text-sm text-gray-500 border-b">Popular Searches</div>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-purple-light">Summer Dresses</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-purple-light">Casual Shirts</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-purple-light">Designer Jeans</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Icons -->
                <div class="flex items-center space-x-4">
                    <button class="md:hidden text-gray-700 hover:text-primary cursor-pointer transition duration-300" id="mobile-search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                    <div class="relative">
                        <button class="text-gray-700 hover:text-primary cursor-pointer transition duration-300 relative" id="wishlist-btn">
                            <i class="fas fa-heart"></i>
                            <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" id="wishlist-count">0</span>
                        </button>
                    </div>
                    <div class="relative">
                        <button class="text-gray-700 hover:text-primary cursor-pointer transition duration-300 relative" id="cart-btn">
                            <i class="fas fa-shopping-bag"></i>
                            <span class="absolute -top-2 -right-2 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" id="cart-count">0</span>
                        </button>
                        <!-- Cart Dropdown -->
                        <div id="cart-dropdown" class="absolute top-full right-0 mt-2 w-80 bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl opacity-0 invisible transition-all duration-300 z-50 border border-white/20">
                            <div class="p-4">
                                <h3 class="font-semibold text-gray-800 mb-3">Shopping Cart</h3>
                                <div id="cart-items" class="space-y-3 mb-4">
                                    <div class="text-center text-gray-500 py-8">Your cart is empty</div>
                                </div>
                                <div class="border-t pt-3">
                                    <div class="flex justify-between items-center mb-3">
                                        <span class="font-semibold">Total:</span>
                                        <span class="font-bold text-primary" id="cart-total">$0.00</span>
                                    </div>
                                    <button class="w-full bg-primary text-white py-2 rounded-lg hover:bg-accent transition duration-300">
                                        Checkout
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Auth Buttons -->
                    <div class="hidden lg:flex items-center space-x-3">
                        <button onclick="openLoginModal()" class="text-gray-700 hover:text-primary font-medium transition duration-300">
                            Login
                        </button>
                        <button onclick="openSignupModal()" class="bg-primary text-white px-4 py-2 rounded-full font-medium hover:bg-accent transition duration-300">
                            Sign Up
                        </button>
                    </div>

                    <!-- Mobile menu button -->
                    <button id="mobile-menu-btn" class="lg:hidden text-gray-700 hover:text-primary">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="lg:hidden hidden bg-white/95 backdrop-blur-xl border-t border-white/20 rounded-b-2xl">
            <div class="px-4 pt-4 pb-6 space-y-3">
                <!-- Mobile Search -->
                <div class="md:hidden mb-4">
                    <div class="relative">
                        <input type="text" placeholder="Search products..."
                               class="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-primary">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>

                <a href="#" class="block px-3 py-3 text-gray-700 hover:text-primary hover:bg-purple-light rounded-lg transition duration-300 font-medium">Home</a>

                <div class="space-y-2">
                    <button class="w-full flex items-center justify-between px-3 py-3 text-gray-700 hover:text-primary hover:bg-purple-light rounded-lg transition duration-300 font-medium" onclick="toggleMobileSubmenu('women')">
                        Women <i class="fas fa-chevron-down transition-transform duration-300" id="women-arrow"></i>
                    </button>
                    <div id="women-submenu" class="hidden pl-6 space-y-1">
                        <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary">Dresses</a>
                        <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary">Tops</a>
                        <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary">Bottoms</a>
                        <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary">Outerwear</a>
                    </div>
                </div>

                <div class="space-y-2">
                    <button class="w-full flex items-center justify-between px-3 py-3 text-gray-700 hover:text-primary hover:bg-purple-light rounded-lg transition duration-300 font-medium" onclick="toggleMobileSubmenu('men')">
                        Men <i class="fas fa-chevron-down transition-transform duration-300" id="men-arrow"></i>
                    </button>
                    <div id="men-submenu" class="hidden pl-6 space-y-1">
                        <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary">Shirts</a>
                        <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary">Pants</a>
                        <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary">Jackets</a>
                        <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary">Shoes</a>
                    </div>
                </div>

                <a href="#" class="block px-3 py-3 text-gray-700 hover:text-primary hover:bg-purple-light rounded-lg transition duration-300 font-medium">Accessories</a>
                <a href="#" class="block px-3 py-3 text-red-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition duration-300 font-medium">Sale</a>

                <!-- Mobile Auth Buttons -->
                <div class="border-t border-gray-200 pt-4 mt-4 space-y-2">
                    <button onclick="openLoginModal()" class="w-full px-3 py-3 text-gray-700 hover:text-primary hover:bg-purple-light rounded-lg transition duration-300 font-medium text-left">
                        Login
                    </button>
                    <button onclick="openSignupModal()" class="w-full bg-primary text-white px-3 py-3 rounded-lg font-medium hover:bg-accent transition duration-300">
                        Sign Up
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative h-screen overflow-hidden" id="hero-section">
        <!-- Hero Slides -->
        <div class="hero-slider relative h-full">
            <!-- Slide 1 -->
            <div class="hero-slide active absolute inset-0 flex items-center justify-center">
                <div class="absolute inset-0 bg-gradient-to-br from-purple-light via-secondary to-white"></div>
                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-primary/10"></div>

                <!-- Floating Elements -->
                <div class="absolute top-20 left-10 w-20 h-20 bg-primary/20 rounded-full animate-float"></div>
                <div class="absolute bottom-32 right-20 w-16 h-16 bg-accent/30 rounded-full animate-float" style="animation-delay: 2s;"></div>
                <div class="absolute top-1/3 right-1/4 w-12 h-12 bg-secondary/40 rounded-full animate-float" style="animation-delay: 4s;"></div>

                <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex items-center h-full">
                    <div class="grid lg:grid-cols-2 gap-12 items-center w-full">
                        <div class="text-left space-y-8">
                            <div class="space-y-4">
                                <span class="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium tracking-wide uppercase">New Collection</span>
                                <h1 class="text-5xl lg:text-7xl font-bold text-gray-900 leading-tight font-display">
                                    Summer
                                    <span class="text-primary block">Elegance</span>
                                </h1>
                                <p class="text-xl text-gray-600 max-w-lg leading-relaxed">
                                    Discover our curated collection of premium summer fashion. From elegant dresses to casual chic, find your perfect style.
                                </p>
                            </div>
                            <div class="flex flex-col sm:flex-row gap-4">
                                <button class="group bg-primary text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-accent transition-all duration-300 transform hover:scale-105 hover:shadow-xl">
                                    <span class="flex items-center justify-center">
                                        Shop Collection
                                        <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                                    </span>
                                </button>
                                <button class="border-2 border-primary text-primary px-8 py-4 rounded-full text-lg font-semibold hover:bg-primary hover:text-white transition-all duration-300">
                                    View Lookbook
                                </button>
                            </div>
                            <div class="flex items-center space-x-8 text-sm text-gray-500">
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-shipping-fast text-primary"></i>
                                    <span>Free Shipping</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-undo text-primary"></i>
                                    <span>Easy Returns</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-star text-primary"></i>
                                    <span>4.9/5 Rating</span>
                                </div>
                            </div>
                        </div>

                        <!-- Hero Image -->
                        <div class="relative lg:block hidden">
                            <div class="relative">
                                <!-- Main Image Placeholder -->
                                <div class="w-full h-96 bg-gradient-to-br from-secondary to-accent rounded-3xl shadow-2xl transform rotate-3 hover:rotate-0 transition-transform duration-500">
                                    <div class="absolute inset-0 bg-gradient-to-t from-primary/20 to-transparent rounded-3xl"></div>
                                    <div class="absolute bottom-6 left-6 text-white">
                                        <div class="text-sm font-medium opacity-90">Featured</div>
                                        <div class="text-lg font-bold">Summer Dress</div>
                                        <div class="text-sm opacity-75">Starting at $89</div>
                                    </div>
                                </div>

                                <!-- Floating Product Cards -->
                                <div class="absolute -top-4 -right-4 bg-white rounded-2xl shadow-xl p-4 animate-float">
                                    <div class="w-16 h-16 bg-purple-light rounded-xl mb-2"></div>
                                    <div class="text-xs font-medium text-gray-800">New Arrival</div>
                                    <div class="text-xs text-gray-500">$129.99</div>
                                </div>

                                <div class="absolute -bottom-4 -left-4 bg-white rounded-2xl shadow-xl p-4 animate-float" style="animation-delay: 1s;">
                                    <div class="w-16 h-16 bg-accent/30 rounded-xl mb-2"></div>
                                    <div class="text-xs font-medium text-gray-800">Best Seller</div>
                                    <div class="text-xs text-gray-500">$79.99</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 2 -->
            <div class="hero-slide absolute inset-0 flex items-center justify-center opacity-0">
                <div class="absolute inset-0 bg-gradient-to-br from-accent/20 via-primary/10 to-purple-dark/5"></div>

                <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex items-center h-full">
                    <div class="text-center space-y-8 w-full">
                        <div class="space-y-4">
                            <span class="inline-block px-4 py-2 bg-red-100 text-red-600 rounded-full text-sm font-medium tracking-wide uppercase">Limited Time</span>
                            <h1 class="text-5xl lg:text-7xl font-bold text-gray-900 leading-tight font-display">
                                Up to <span class="text-red-500">70% Off</span>
                                <span class="block text-primary">Sale Event</span>
                            </h1>
                            <p class="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
                                Don't miss our biggest sale of the season. Premium fashion at unbeatable prices.
                            </p>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-4 justify-center">
                            <button class="bg-red-500 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-red-600 transition-all duration-300 transform hover:scale-105">
                                Shop Sale Now
                            </button>
                            <button class="border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-full text-lg font-semibold hover:border-primary hover:text-primary transition-all duration-300">
                                View All Deals
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 3 -->
            <div class="hero-slide absolute inset-0 flex items-center justify-center opacity-0">
                <div class="absolute inset-0 bg-gradient-to-br from-primary/10 via-accent/5 to-secondary/20"></div>

                <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex items-center h-full">
                    <div class="text-center space-y-8 w-full">
                        <div class="space-y-4">
                            <span class="inline-block px-4 py-2 bg-green-100 text-green-600 rounded-full text-sm font-medium tracking-wide uppercase">Sustainable Fashion</span>
                            <h1 class="text-5xl lg:text-7xl font-bold text-gray-900 leading-tight font-display">
                                Eco-Friendly
                                <span class="text-primary block">Collection</span>
                            </h1>
                            <p class="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
                                Fashion that cares for the planet. Discover our sustainable and ethically made clothing line.
                            </p>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-4 justify-center">
                            <button class="bg-green-500 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-green-600 transition-all duration-300 transform hover:scale-105">
                                Explore Eco Line
                            </button>
                            <button class="border-2 border-green-500 text-green-500 px-8 py-4 rounded-full text-lg font-semibold hover:bg-green-500 hover:text-white transition-all duration-300">
                                Learn More
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hero Navigation -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3 z-20">
            <button class="hero-dot active w-3 h-3 rounded-full bg-primary transition-all duration-300" data-slide="0"></button>
            <button class="hero-dot w-3 h-3 rounded-full bg-gray-300 hover:bg-primary transition-all duration-300" data-slide="1"></button>
            <button class="hero-dot w-3 h-3 rounded-full bg-gray-300 hover:bg-primary transition-all duration-300" data-slide="2"></button>
        </div>

        <!-- Hero Arrow Navigation -->
        <button class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 backdrop-blur-sm hover:bg-white text-primary p-3 rounded-full shadow-lg transition-all duration-300 z-20" id="hero-prev">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 backdrop-blur-sm hover:bg-white text-primary p-3 rounded-full shadow-lg transition-all duration-300 z-20" id="hero-next">
            <i class="fas fa-chevron-right"></i>
        </button>
    </section>

    <!-- Featured Categories -->
    <section class="py-20 bg-gradient-to-b from-gray-50 to-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <span class="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium tracking-wide uppercase mb-4">Categories</span>
                <h3 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 font-display">Shop by Category</h3>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">Discover our carefully curated collections designed for every style and occasion</p>
            </div>

            <!-- Main Categories Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
                <!-- Women's Category -->
                <div class="group cursor-pointer">
                    <div class="relative h-96 bg-gradient-to-br from-secondary to-purple-light rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                        <div class="absolute inset-0 bg-gradient-to-t from-primary/60 via-transparent to-transparent"></div>
                        <div class="absolute inset-0 bg-black/10 group-hover:bg-black/5 transition-all duration-300"></div>

                        <!-- Category Icon -->
                        <div class="absolute top-6 right-6 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                            <i class="fas fa-female text-white text-xl"></i>
                        </div>

                        <!-- Category Content -->
                        <div class="absolute bottom-6 left-6 right-6 text-white">
                            <h4 class="text-3xl font-bold mb-2 font-display">Women's</h4>
                            <p class="text-sm opacity-90 mb-4">Elegant & Trendy Fashion</p>
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium">500+ Items</span>
                                <div class="flex items-center space-x-1">
                                    <span class="text-sm">Shop Now</span>
                                    <i class="fas fa-arrow-right group-hover:translate-x-1 transition-transform duration-300"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Hover Overlay -->
                        <div class="absolute inset-0 bg-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                </div>

                <!-- Men's Category -->
                <div class="group cursor-pointer">
                    <div class="relative h-96 bg-gradient-to-br from-accent to-primary rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                        <div class="absolute inset-0 bg-gradient-to-t from-purple-dark/60 via-transparent to-transparent"></div>
                        <div class="absolute inset-0 bg-black/10 group-hover:bg-black/5 transition-all duration-300"></div>

                        <!-- Category Icon -->
                        <div class="absolute top-6 right-6 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                            <i class="fas fa-male text-white text-xl"></i>
                        </div>

                        <!-- Category Content -->
                        <div class="absolute bottom-6 left-6 right-6 text-white">
                            <h4 class="text-3xl font-bold mb-2 font-display">Men's</h4>
                            <p class="text-sm opacity-90 mb-4">Classic & Modern Style</p>
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium">350+ Items</span>
                                <div class="flex items-center space-x-1">
                                    <span class="text-sm">Shop Now</span>
                                    <i class="fas fa-arrow-right group-hover:translate-x-1 transition-transform duration-300"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Hover Overlay -->
                        <div class="absolute inset-0 bg-accent/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                </div>

                <!-- Accessories Category -->
                <div class="group cursor-pointer">
                    <div class="relative h-96 bg-gradient-to-br from-purple-light to-secondary rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                        <div class="absolute inset-0 bg-gradient-to-t from-primary/60 via-transparent to-transparent"></div>
                        <div class="absolute inset-0 bg-black/10 group-hover:bg-black/5 transition-all duration-300"></div>

                        <!-- Category Icon -->
                        <div class="absolute top-6 right-6 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                            <i class="fas fa-gem text-white text-xl"></i>
                        </div>

                        <!-- Category Content -->
                        <div class="absolute bottom-6 left-6 right-6 text-white">
                            <h4 class="text-3xl font-bold mb-2 font-display">Accessories</h4>
                            <p class="text-sm opacity-90 mb-4">Complete Your Look</p>
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium">200+ Items</span>
                                <div class="flex items-center space-x-1">
                                    <span class="text-sm">Shop Now</span>
                                    <i class="fas fa-arrow-right group-hover:translate-x-1 transition-transform duration-300"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Hover Overlay -->
                        <div class="absolute inset-0 bg-secondary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                </div>
            </div>

            <!-- Sub Categories -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div class="group cursor-pointer bg-white rounded-2xl p-6 shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center mb-4 group-hover:bg-primary/20 transition-colors duration-300">
                        <i class="fas fa-tshirt text-primary text-xl"></i>
                    </div>
                    <h5 class="font-semibold text-gray-800 mb-2">Casual Wear</h5>
                    <p class="text-sm text-gray-600">Everyday comfort</p>
                </div>

                <div class="group cursor-pointer bg-white rounded-2xl p-6 shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="w-12 h-12 bg-accent/10 rounded-xl flex items-center justify-center mb-4 group-hover:bg-accent/20 transition-colors duration-300">
                        <i class="fas fa-user-tie text-accent text-xl"></i>
                    </div>
                    <h5 class="font-semibold text-gray-800 mb-2">Formal Wear</h5>
                    <p class="text-sm text-gray-600">Professional style</p>
                </div>

                <div class="group cursor-pointer bg-white rounded-2xl p-6 shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="w-12 h-12 bg-secondary/30 rounded-xl flex items-center justify-center mb-4 group-hover:bg-secondary/50 transition-colors duration-300">
                        <i class="fas fa-running text-primary text-xl"></i>
                    </div>
                    <h5 class="font-semibold text-gray-800 mb-2">Activewear</h5>
                    <p class="text-sm text-gray-600">Sport & fitness</p>
                </div>

                <div class="group cursor-pointer bg-white rounded-2xl p-6 shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center mb-4 group-hover:bg-red-200 transition-colors duration-300">
                        <i class="fas fa-tags text-red-500 text-xl"></i>
                    </div>
                    <h5 class="font-semibold text-gray-800 mb-2">Sale Items</h5>
                    <p class="text-sm text-gray-600">Up to 70% off</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <span class="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium tracking-wide uppercase mb-4">Featured</span>
                <h3 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 font-display">Trending Products</h3>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">Discover our most popular items loved by thousands of customers worldwide</p>
            </div>

            <!-- Product Filter Tabs -->
            <div class="flex justify-center mb-12">
                <div class="bg-gray-100 rounded-full p-1 inline-flex">
                    <button class="product-filter-btn active px-6 py-3 rounded-full text-sm font-medium transition-all duration-300" data-filter="all">All Products</button>
                    <button class="product-filter-btn px-6 py-3 rounded-full text-sm font-medium transition-all duration-300" data-filter="women">Women</button>
                    <button class="product-filter-btn px-6 py-3 rounded-full text-sm font-medium transition-all duration-300" data-filter="men">Men</button>
                    <button class="product-filter-btn px-6 py-3 rounded-full text-sm font-medium transition-all duration-300" data-filter="accessories">Accessories</button>
                    <button class="product-filter-btn px-6 py-3 rounded-full text-sm font-medium transition-all duration-300" data-filter="sale">Sale</button>
                </div>
            </div>

            <div class="flex justify-between items-center mb-8">
                <div class="flex items-center space-x-4">
                    <span class="text-gray-600">Showing <span id="product-count">12</span> products</span>
                    <select class="border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary" id="sort-select">
                        <option value="featured">Featured</option>
                        <option value="price-low">Price: Low to High</option>
                        <option value="price-high">Price: High to Low</option>
                        <option value="newest">Newest First</option>
                        <option value="rating">Highest Rated</option>
                    </select>
                </div>
                <div class="flex space-x-2">
                    <button id="prev-btn" class="p-3 rounded-full bg-gray-100 text-gray-600 hover:bg-primary hover:text-white transition-all duration-300 shadow-md hover:shadow-lg">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button id="next-btn" class="p-3 rounded-full bg-gray-100 text-gray-600 hover:bg-primary hover:text-white transition-all duration-300 shadow-md hover:shadow-lg">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8" id="products-grid">
                <!-- Product 1 -->
                <div class="product-card group cursor-pointer" data-category="women" data-price="89.99" data-rating="4.8">
                    <div class="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden">
                        <div class="relative h-72 bg-gradient-to-br from-secondary to-purple-light overflow-hidden">
                            <!-- Product Badges -->
                            <div class="absolute top-4 left-4 z-10">
                                <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium">New</span>
                            </div>

                            <!-- Wishlist & Quick View -->
                            <div class="absolute top-4 right-4 z-10 flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                                <button class="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors duration-300 wishlist-btn">
                                    <i class="fas fa-heart text-gray-600 hover:text-red-500 text-sm"></i>
                                </button>
                                <button class="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors duration-300 quick-view-btn">
                                    <i class="fas fa-eye text-gray-600 hover:text-primary text-sm"></i>
                                </button>
                            </div>

                            <!-- Product Image Overlay -->
                            <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                            <!-- Quick Add to Cart -->
                            <div class="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-300">
                                <button class="w-full bg-primary text-white py-2 rounded-lg font-medium hover:bg-accent transition-colors duration-300 add-to-cart-btn">
                                    Add to Cart
                                </button>
                            </div>
                        </div>

                        <div class="p-6">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-xs text-gray-500 uppercase tracking-wide">Women's Dress</span>
                                <div class="flex items-center space-x-1">
                                    <div class="flex text-yellow-400">
                                        <i class="fas fa-star text-xs"></i>
                                        <i class="fas fa-star text-xs"></i>
                                        <i class="fas fa-star text-xs"></i>
                                        <i class="fas fa-star text-xs"></i>
                                        <i class="fas fa-star text-xs"></i>
                                    </div>
                                    <span class="text-xs text-gray-500">(4.8)</span>
                                </div>
                            </div>
                            <h4 class="font-semibold text-gray-800 mb-2 group-hover:text-primary transition-colors duration-300">Elegant Summer Dress</h4>
                            <p class="text-sm text-gray-600 mb-3">Flowing maxi dress perfect for summer occasions</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <span class="text-lg font-bold text-primary">$89.99</span>
                                    <span class="text-sm text-gray-400 line-through">$119.99</span>
                                </div>
                                <div class="flex space-x-1">
                                    <div class="w-4 h-4 bg-blue-500 rounded-full border-2 border-white shadow-sm"></div>
                                    <div class="w-4 h-4 bg-red-500 rounded-full border-2 border-white shadow-sm"></div>
                                    <div class="w-4 h-4 bg-green-500 rounded-full border-2 border-white shadow-sm"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Product 2 -->
                <div class="product-card group cursor-pointer" data-category="men" data-price="45.99" data-rating="4.6">
                    <div class="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden">
                        <div class="relative h-72 bg-gradient-to-br from-accent to-primary overflow-hidden">
                            <!-- Product Badges -->
                            <div class="absolute top-4 left-4 z-10">
                                <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">Sale</span>
                            </div>

                            <!-- Wishlist & Quick View -->
                            <div class="absolute top-4 right-4 z-10 flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                                <button class="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors duration-300 wishlist-btn">
                                    <i class="fas fa-heart text-gray-600 hover:text-red-500 text-sm"></i>
                                </button>
                                <button class="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors duration-300 quick-view-btn">
                                    <i class="fas fa-eye text-gray-600 hover:text-primary text-sm"></i>
                                </button>
                            </div>

                            <!-- Product Image Overlay -->
                            <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                            <!-- Quick Add to Cart -->
                            <div class="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-300">
                                <button class="w-full bg-primary text-white py-2 rounded-lg font-medium hover:bg-accent transition-colors duration-300 add-to-cart-btn">
                                    Add to Cart
                                </button>
                            </div>
                        </div>

                        <div class="p-6">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-xs text-gray-500 uppercase tracking-wide">Men's Shirt</span>
                                <div class="flex items-center space-x-1">
                                    <div class="flex text-yellow-400">
                                        <i class="fas fa-star text-xs"></i>
                                        <i class="fas fa-star text-xs"></i>
                                        <i class="fas fa-star text-xs"></i>
                                        <i class="fas fa-star text-xs"></i>
                                        <i class="far fa-star text-xs"></i>
                                    </div>
                                    <span class="text-xs text-gray-500">(4.6)</span>
                                </div>
                            </div>
                            <h4 class="font-semibold text-gray-800 mb-2 group-hover:text-primary transition-colors duration-300">Premium Cotton Shirt</h4>
                            <p class="text-sm text-gray-600 mb-3">Classic fit shirt made from premium cotton</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <span class="text-lg font-bold text-primary">$45.99</span>
                                    <span class="text-sm text-gray-400 line-through">$65.99</span>
                                </div>
                                <div class="flex space-x-1">
                                    <div class="w-4 h-4 bg-white rounded-full border-2 border-gray-300 shadow-sm"></div>
                                    <div class="w-4 h-4 bg-blue-900 rounded-full border-2 border-white shadow-sm"></div>
                                    <div class="w-4 h-4 bg-gray-800 rounded-full border-2 border-white shadow-sm"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Product 3 -->
                <div class="product-card group cursor-pointer" data-category="women" data-price="129.99" data-rating="4.9">
                    <div class="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden">
                        <div class="relative h-72 bg-gradient-to-br from-purple-light to-secondary overflow-hidden">
                            <!-- Product Badges -->
                            <div class="absolute top-4 left-4 z-10">
                                <span class="bg-purple-500 text-white text-xs px-2 py-1 rounded-full font-medium">Bestseller</span>
                            </div>

                            <!-- Wishlist & Quick View -->
                            <div class="absolute top-4 right-4 z-10 flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                                <button class="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors duration-300 wishlist-btn">
                                    <i class="fas fa-heart text-gray-600 hover:text-red-500 text-sm"></i>
                                </button>
                                <button class="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors duration-300 quick-view-btn">
                                    <i class="fas fa-eye text-gray-600 hover:text-primary text-sm"></i>
                                </button>
                            </div>

                            <!-- Product Image Overlay -->
                            <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                            <!-- Quick Add to Cart -->
                            <div class="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-300">
                                <button class="w-full bg-primary text-white py-2 rounded-lg font-medium hover:bg-accent transition-colors duration-300 add-to-cart-btn">
                                    Add to Cart
                                </button>
                            </div>
                        </div>

                        <div class="p-6">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-xs text-gray-500 uppercase tracking-wide">Women's Jeans</span>
                                <div class="flex items-center space-x-1">
                                    <div class="flex text-yellow-400">
                                        <i class="fas fa-star text-xs"></i>
                                        <i class="fas fa-star text-xs"></i>
                                        <i class="fas fa-star text-xs"></i>
                                        <i class="fas fa-star text-xs"></i>
                                        <i class="fas fa-star text-xs"></i>
                                    </div>
                                    <span class="text-xs text-gray-500">(4.9)</span>
                                </div>
                            </div>
                            <h4 class="font-semibold text-gray-800 mb-2 group-hover:text-primary transition-colors duration-300">Designer Skinny Jeans</h4>
                            <p class="text-sm text-gray-600 mb-3">High-waisted skinny jeans with stretch comfort</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <span class="text-lg font-bold text-primary">$129.99</span>
                                </div>
                                <div class="flex space-x-1">
                                    <div class="w-4 h-4 bg-blue-600 rounded-full border-2 border-white shadow-sm"></div>
                                    <div class="w-4 h-4 bg-gray-800 rounded-full border-2 border-white shadow-sm"></div>
                                    <div class="w-4 h-4 bg-indigo-600 rounded-full border-2 border-white shadow-sm"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Product 4 -->
                <div class="product-card group cursor-pointer" data-category="accessories" data-price="199.99" data-rating="4.7">
                    <div class="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden">
                        <div class="relative h-72 bg-gradient-to-br from-accent to-purple-dark overflow-hidden">
                            <!-- Product Badges -->
                            <div class="absolute top-4 left-4 z-10">
                                <span class="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-medium">Limited</span>
                            </div>

                            <!-- Wishlist & Quick View -->
                            <div class="absolute top-4 right-4 z-10 flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                                <button class="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors duration-300 wishlist-btn">
                                    <i class="fas fa-heart text-gray-600 hover:text-red-500 text-sm"></i>
                                </button>
                                <button class="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors duration-300 quick-view-btn">
                                    <i class="fas fa-eye text-gray-600 hover:text-primary text-sm"></i>
                                </button>
                            </div>

                            <!-- Product Image Overlay -->
                            <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                            <!-- Quick Add to Cart -->
                            <div class="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-300">
                                <button class="w-full bg-primary text-white py-2 rounded-lg font-medium hover:bg-accent transition-colors duration-300 add-to-cart-btn">
                                    Add to Cart
                                </button>
                            </div>
                        </div>

                        <div class="p-6">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-xs text-gray-500 uppercase tracking-wide">Luxury Bag</span>
                                <div class="flex items-center space-x-1">
                                    <div class="flex text-yellow-400">
                                        <i class="fas fa-star text-xs"></i>
                                        <i class="fas fa-star text-xs"></i>
                                        <i class="fas fa-star text-xs"></i>
                                        <i class="fas fa-star text-xs"></i>
                                        <i class="fas fa-star-half-alt text-xs"></i>
                                    </div>
                                    <span class="text-xs text-gray-500">(4.7)</span>
                                </div>
                            </div>
                            <h4 class="font-semibold text-gray-800 mb-2 group-hover:text-primary transition-colors duration-300">Luxury Leather Handbag</h4>
                            <p class="text-sm text-gray-600 mb-3">Premium leather handbag with gold hardware</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <span class="text-lg font-bold text-primary">$199.99</span>
                                </div>
                                <div class="flex space-x-1">
                                    <div class="w-4 h-4 bg-black rounded-full border-2 border-white shadow-sm"></div>
                                    <div class="w-4 h-4 bg-amber-700 rounded-full border-2 border-white shadow-sm"></div>
                                    <div class="w-4 h-4 bg-red-800 rounded-full border-2 border-white shadow-sm"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-purple-light/20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <span class="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium tracking-wide uppercase mb-4">Why Choose Us</span>
                <h3 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 font-display">The Luxe Fashion Difference</h3>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">Experience premium fashion with unmatched quality and service</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center group">
                    <div class="w-20 h-20 bg-gradient-to-br from-primary to-accent rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-shipping-fast text-white text-2xl"></i>
                    </div>
                    <h4 class="text-xl font-bold text-gray-900 mb-3">Free Shipping</h4>
                    <p class="text-gray-600">Free shipping on all orders over $75. Fast delivery worldwide.</p>
                </div>

                <div class="text-center group">
                    <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-undo text-white text-2xl"></i>
                    </div>
                    <h4 class="text-xl font-bold text-gray-900 mb-3">Easy Returns</h4>
                    <p class="text-gray-600">30-day hassle-free returns. Your satisfaction is guaranteed.</p>
                </div>

                <div class="text-center group">
                    <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-award text-white text-2xl"></i>
                    </div>
                    <h4 class="text-xl font-bold text-gray-900 mb-3">Premium Quality</h4>
                    <p class="text-gray-600">Carefully curated collection of high-quality fashion items.</p>
                </div>

                <div class="text-center group">
                    <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-headset text-white text-2xl"></i>
                    </div>
                    <h4 class="text-xl font-bold text-gray-900 mb-3">24/7 Support</h4>
                    <p class="text-gray-600">Round-the-clock customer support for all your needs.</p>
                </div>
            </div>

            <!-- Stats Section -->
            <div class="mt-20 bg-white rounded-3xl shadow-xl p-8 lg:p-12">
                <div class="grid grid-cols-2 lg:grid-cols-4 gap-8 text-center">
                    <div>
                        <div class="text-4xl font-bold text-primary mb-2">50K+</div>
                        <div class="text-gray-600">Happy Customers</div>
                    </div>
                    <div>
                        <div class="text-4xl font-bold text-primary mb-2">1000+</div>
                        <div class="text-gray-600">Products</div>
                    </div>
                    <div>
                        <div class="text-4xl font-bold text-primary mb-2">5</div>
                        <div class="text-gray-600">Years Experience</div>
                    </div>
                    <div>
                        <div class="text-4xl font-bold text-primary mb-2">4.9</div>
                        <div class="text-gray-600">Average Rating</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <span class="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium tracking-wide uppercase mb-4">Testimonials</span>
                <h3 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 font-display">What Our Customers Say</h3>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">Real reviews from real customers who love our fashion</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Testimonial 1 -->
                <div class="bg-gradient-to-br from-purple-light/30 to-secondary/20 rounded-2xl p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="flex items-center mb-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold text-xl mr-4">
                            S
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">Sarah Johnson</h4>
                            <p class="text-sm text-gray-600">Fashion Blogger</p>
                        </div>
                    </div>
                    <div class="flex text-yellow-400 mb-4">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <p class="text-gray-700 italic">"Absolutely love the quality and style! The dresses fit perfectly and the customer service is exceptional. Will definitely shop here again."</p>
                </div>

                <!-- Testimonial 2 -->
                <div class="bg-gradient-to-br from-accent/20 to-primary/10 rounded-2xl p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="flex items-center mb-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4">
                            M
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">Michael Chen</h4>
                            <p class="text-sm text-gray-600">Business Executive</p>
                        </div>
                    </div>
                    <div class="flex text-yellow-400 mb-4">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <p class="text-gray-700 italic">"Great selection of professional wear. The shirts are high quality and the fit is perfect. Fast shipping and excellent packaging."</p>
                </div>

                <!-- Testimonial 3 -->
                <div class="bg-gradient-to-br from-secondary/30 to-purple-light/20 rounded-2xl p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="flex items-center mb-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-pink-500 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4">
                            E
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">Emma Rodriguez</h4>
                            <p class="text-sm text-gray-600">Designer</p>
                        </div>
                    </div>
                    <div class="flex text-yellow-400 mb-4">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="far fa-star"></i>
                    </div>
                    <p class="text-gray-700 italic">"Beautiful accessories and trendy pieces. The website is easy to navigate and the checkout process is smooth. Highly recommended!"</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-16 bg-gradient-to-r from-primary to-accent">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h3 class="text-3xl font-bold text-white mb-4">Stay in Style</h3>
            <p class="text-secondary mb-8 text-lg">Subscribe to our newsletter for exclusive offers and latest fashion trends</p>
            <div class="flex flex-col sm:flex-row max-w-md mx-auto">
                <input type="email" placeholder="Enter your email" class="flex-1 px-4 py-3 rounded-l-full sm:rounded-r-none rounded-r-full focus:outline-none focus:ring-2 focus:ring-secondary">
                <button class="bg-white text-primary px-6 py-3 rounded-r-full sm:rounded-l-none rounded-l-full font-semibold hover:bg-secondary transition duration-300 mt-2 sm:mt-0">
                    Subscribe
                </button>
            </div>
        </div>
    </section>

    <!-- Authentication Modals -->
    <!-- Login Modal -->
    <div id="login-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden flex items-center justify-center p-4">
        <div class="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl w-full max-w-md border border-white/20 transform scale-95 transition-all duration-300" id="login-modal-content">
            <div class="p-8">
                <!-- Header -->
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2 font-display">Welcome Back</h2>
                    <p class="text-gray-600">Sign in to your Luxe Fashion account</p>
                </div>

                <!-- Login Form -->
                <form id="login-form" class="space-y-6">
                    <!-- Email Field -->
                    <div class="relative">
                        <input type="email" id="login-email" required
                               class="w-full px-4 py-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300 peer placeholder-transparent">
                        <label for="login-email" class="absolute left-4 top-4 text-gray-500 transition-all duration-300 peer-placeholder-shown:top-4 peer-placeholder-shown:text-base peer-focus:top-1 peer-focus:text-sm peer-focus:text-primary peer-valid:top-1 peer-valid:text-sm">
                            Email Address
                        </label>
                    </div>

                    <!-- Password Field -->
                    <div class="relative">
                        <input type="password" id="login-password" required
                               class="w-full px-4 py-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300 peer placeholder-transparent">
                        <label for="login-password" class="absolute left-4 top-4 text-gray-500 transition-all duration-300 peer-placeholder-shown:top-4 peer-placeholder-shown:text-base peer-focus:top-1 peer-focus:text-sm peer-focus:text-primary peer-valid:top-1 peer-valid:text-sm">
                            Password
                        </label>
                        <button type="button" class="absolute right-4 top-4 text-gray-400 hover:text-gray-600" onclick="togglePasswordVisibility('login-password')">
                            <i class="fas fa-eye" id="login-password-icon"></i>
                        </button>
                    </div>

                    <!-- Remember Me & Forgot Password -->
                    <div class="flex items-center justify-between">
                        <label class="flex items-center">
                            <input type="checkbox" class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary">
                            <span class="ml-2 text-sm text-gray-600">Remember me</span>
                        </label>
                        <a href="#" class="text-sm text-primary hover:text-accent transition-colors duration-300">Forgot password?</a>
                    </div>

                    <!-- Login Button -->
                    <button type="submit" class="w-full bg-primary text-white py-4 rounded-2xl font-semibold hover:bg-accent transition-all duration-300 transform hover:scale-105">
                        Sign In
                    </button>
                </form>

                <!-- Social Login -->
                <div class="mt-8">
                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-200"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-4 bg-white text-gray-500">Or continue with</span>
                        </div>
                    </div>

                    <div class="mt-6 grid grid-cols-3 gap-3">
                        <button class="flex justify-center items-center px-4 py-3 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors duration-300">
                            <i class="fab fa-google text-red-500 text-xl"></i>
                        </button>
                        <button class="flex justify-center items-center px-4 py-3 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors duration-300">
                            <i class="fab fa-facebook text-blue-600 text-xl"></i>
                        </button>
                        <button class="flex justify-center items-center px-4 py-3 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors duration-300">
                            <i class="fab fa-apple text-gray-900 text-xl"></i>
                        </button>
                    </div>
                </div>

                <!-- Switch to Signup -->
                <div class="mt-8 text-center">
                    <p class="text-gray-600">
                        Don't have an account?
                        <button onclick="switchToSignup()" class="text-primary hover:text-accent font-medium transition-colors duration-300">Sign up</button>
                    </p>
                </div>

                <!-- Close Button -->
                <button onclick="closeAuthModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors duration-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Signup Modal -->
    <div id="signup-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden flex items-center justify-center p-4">
        <div class="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl w-full max-w-md border border-white/20 transform scale-95 transition-all duration-300" id="signup-modal-content">
            <div class="p-8">
                <!-- Header -->
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2 font-display">Join Luxe Fashion</h2>
                    <p class="text-gray-600">Create your account and start shopping</p>
                </div>

                <!-- Signup Form -->
                <form id="signup-form" class="space-y-6">
                    <!-- Full Name Field -->
                    <div class="relative">
                        <input type="text" id="signup-name" required
                               class="w-full px-4 py-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300 peer placeholder-transparent">
                        <label for="signup-name" class="absolute left-4 top-4 text-gray-500 transition-all duration-300 peer-placeholder-shown:top-4 peer-placeholder-shown:text-base peer-focus:top-1 peer-focus:text-sm peer-focus:text-primary peer-valid:top-1 peer-valid:text-sm">
                            Full Name
                        </label>
                    </div>

                    <!-- Email Field -->
                    <div class="relative">
                        <input type="email" id="signup-email" required
                               class="w-full px-4 py-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300 peer placeholder-transparent">
                        <label for="signup-email" class="absolute left-4 top-4 text-gray-500 transition-all duration-300 peer-placeholder-shown:top-4 peer-placeholder-shown:text-base peer-focus:top-1 peer-focus:text-sm peer-focus:text-primary peer-valid:top-1 peer-valid:text-sm">
                            Email Address
                        </label>
                        <div id="email-validation" class="mt-1 text-sm hidden">
                            <span class="text-red-500">Please enter a valid email address</span>
                        </div>
                    </div>

                    <!-- Password Field -->
                    <div class="relative">
                        <input type="password" id="signup-password" required
                               class="w-full px-4 py-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300 peer placeholder-transparent">
                        <label for="signup-password" class="absolute left-4 top-4 text-gray-500 transition-all duration-300 peer-placeholder-shown:top-4 peer-placeholder-shown:text-base peer-focus:top-1 peer-focus:text-sm peer-focus:text-primary peer-valid:top-1 peer-valid:text-sm">
                            Password
                        </label>
                        <button type="button" class="absolute right-4 top-4 text-gray-400 hover:text-gray-600" onclick="togglePasswordVisibility('signup-password')">
                            <i class="fas fa-eye" id="signup-password-icon"></i>
                        </button>
                        <div id="password-strength" class="mt-1 text-sm hidden">
                            <div class="flex space-x-1">
                                <div class="h-1 w-1/4 bg-gray-200 rounded" id="strength-1"></div>
                                <div class="h-1 w-1/4 bg-gray-200 rounded" id="strength-2"></div>
                                <div class="h-1 w-1/4 bg-gray-200 rounded" id="strength-3"></div>
                                <div class="h-1 w-1/4 bg-gray-200 rounded" id="strength-4"></div>
                            </div>
                            <span id="strength-text" class="text-gray-500">Password strength</span>
                        </div>
                    </div>

                    <!-- Confirm Password Field -->
                    <div class="relative">
                        <input type="password" id="signup-confirm-password" required
                               class="w-full px-4 py-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300 peer placeholder-transparent">
                        <label for="signup-confirm-password" class="absolute left-4 top-4 text-gray-500 transition-all duration-300 peer-placeholder-shown:top-4 peer-placeholder-shown:text-base peer-focus:top-1 peer-focus:text-sm peer-focus:text-primary peer-valid:top-1 peer-valid:text-sm">
                            Confirm Password
                        </label>
                        <div id="password-match" class="mt-1 text-sm hidden">
                            <span class="text-red-500">Passwords do not match</span>
                        </div>
                    </div>

                    <!-- Terms and Privacy -->
                    <div class="space-y-3">
                        <label class="flex items-start">
                            <input type="checkbox" id="terms-checkbox" required class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary mt-1">
                            <span class="ml-2 text-sm text-gray-600">
                                I agree to the <a href="#" class="text-primary hover:text-accent">Terms of Service</a> and <a href="#" class="text-primary hover:text-accent">Privacy Policy</a>
                            </span>
                        </label>
                        <label class="flex items-start">
                            <input type="checkbox" class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary mt-1">
                            <span class="ml-2 text-sm text-gray-600">
                                Subscribe to our newsletter for exclusive offers and updates
                            </span>
                        </label>
                    </div>

                    <!-- Signup Button -->
                    <button type="submit" class="w-full bg-primary text-white py-4 rounded-2xl font-semibold hover:bg-accent transition-all duration-300 transform hover:scale-105">
                        Create Account
                    </button>
                </form>

                <!-- Social Signup -->
                <div class="mt-8">
                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-200"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-4 bg-white text-gray-500">Or sign up with</span>
                        </div>
                    </div>

                    <div class="mt-6 grid grid-cols-3 gap-3">
                        <button class="flex justify-center items-center px-4 py-3 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors duration-300">
                            <i class="fab fa-google text-red-500 text-xl"></i>
                        </button>
                        <button class="flex justify-center items-center px-4 py-3 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors duration-300">
                            <i class="fab fa-facebook text-blue-600 text-xl"></i>
                        </button>
                        <button class="flex justify-center items-center px-4 py-3 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors duration-300">
                            <i class="fab fa-apple text-gray-900 text-xl"></i>
                        </button>
                    </div>
                </div>

                <!-- Switch to Login -->
                <div class="mt-8 text-center">
                    <p class="text-gray-600">
                        Already have an account?
                        <button onclick="switchToLogin()" class="text-primary hover:text-accent font-medium transition-colors duration-300">Sign in</button>
                    </p>
                </div>

                <!-- Close Button -->
                <button onclick="closeAuthModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors duration-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div>
                    <h4 class="text-xl font-bold text-secondary mb-4">Luxe Fashion</h4>
                    <p class="text-gray-400 mb-4">Your destination for premium fashion and timeless style.</p>
                    <div class="flex space-x-4">
                        <i class="fab fa-facebook text-secondary hover:text-white cursor-pointer transition duration-300"></i>
                        <i class="fab fa-instagram text-secondary hover:text-white cursor-pointer transition duration-300"></i>
                        <i class="fab fa-twitter text-secondary hover:text-white cursor-pointer transition duration-300"></i>
                        <i class="fab fa-pinterest text-secondary hover:text-white cursor-pointer transition duration-300"></i>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h5 class="font-semibold text-secondary mb-4">Quick Links</h5>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">About Us</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Contact</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Size Guide</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Returns</a></li>
                    </ul>
                </div>

                <!-- Categories -->
                <div>
                    <h5 class="font-semibold text-secondary mb-4">Categories</h5>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Women's Clothing</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Men's Clothing</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Accessories</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Sale Items</a></li>
                    </ul>
                </div>

                <!-- Customer Service -->
                <div>
                    <h5 class="font-semibold text-secondary mb-4">Customer Service</h5>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Help Center</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Shipping Info</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Track Order</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Privacy Policy</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-400">&copy; 2024 Luxe Fashion. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Global state
        let cart = [];
        let wishlist = [];
        let currentHeroSlide = 0;

        // Close announcement bar
        document.getElementById('close-announcement')?.addEventListener('click', function() {
            this.closest('div').style.display = 'none';
            document.getElementById('navbar').style.top = '0';
        });

        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');

        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // Mobile submenu toggle
        window.toggleMobileSubmenu = function(category) {
            const submenu = document.getElementById(category + '-submenu');
            const arrow = document.getElementById(category + '-arrow');

            submenu.classList.toggle('hidden');
            arrow.classList.toggle('rotate-180');
        };

        // Search functionality
        const searchInput = document.getElementById('search-input');
        const searchSuggestions = document.getElementById('search-suggestions');

        searchInput?.addEventListener('focus', () => {
            searchSuggestions.classList.remove('hidden');
        });

        searchInput?.addEventListener('blur', () => {
            setTimeout(() => {
                searchSuggestions.classList.add('hidden');
            }, 200);
        });

        // Cart functionality
        const cartBtn = document.getElementById('cart-btn');
        const cartDropdown = document.getElementById('cart-dropdown');
        const cartCount = document.getElementById('cart-count');
        const cartItems = document.getElementById('cart-items');
        const cartTotal = document.getElementById('cart-total');

        cartBtn?.addEventListener('click', () => {
            cartDropdown.classList.toggle('opacity-0');
            cartDropdown.classList.toggle('invisible');
        });

        // Wishlist functionality
        const wishlistCount = document.getElementById('wishlist-count');

        function updateCartUI() {
            cartCount.textContent = cart.length;
            const total = cart.reduce((sum, item) => sum + item.price, 0);
            cartTotal.textContent = `$${total.toFixed(2)}`;

            if (cart.length === 0) {
                cartItems.innerHTML = '<div class="text-center text-gray-500 py-8">Your cart is empty</div>';
            } else {
                cartItems.innerHTML = cart.map(item => `
                    <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                        <div class="w-12 h-12 bg-secondary rounded-lg"></div>
                        <div class="flex-1">
                            <h4 class="font-medium text-sm">${item.name}</h4>
                            <p class="text-primary font-bold text-sm">$${item.price}</p>
                        </div>
                        <button onclick="removeFromCart(${item.id})" class="text-red-500 hover:text-red-700">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `).join('');
            }
        }

        function updateWishlistUI() {
            wishlistCount.textContent = wishlist.length;
        }

        window.addToCart = function(productId, productName, productPrice) {
            const item = { id: productId, name: productName, price: parseFloat(productPrice) };
            cart.push(item);
            updateCartUI();

            // Show success message
            showNotification('Added to cart!', 'success');
        };

        window.removeFromCart = function(productId) {
            cart = cart.filter(item => item.id !== productId);
            updateCartUI();
        };

        window.toggleWishlist = function(productId, productName) {
            const existingIndex = wishlist.findIndex(item => item.id === productId);

            if (existingIndex > -1) {
                wishlist.splice(existingIndex, 1);
                showNotification('Removed from wishlist', 'info');
            } else {
                wishlist.push({ id: productId, name: productName });
                showNotification('Added to wishlist!', 'success');
            }

            updateWishlistUI();
        };

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-24 right-4 z-50 px-6 py-3 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300 ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                'bg-blue-500 text-white'
            }`;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Hero slider
        const heroSlides = document.querySelectorAll('.hero-slide');
        const heroDots = document.querySelectorAll('.hero-dot');
        const heroPrev = document.getElementById('hero-prev');
        const heroNext = document.getElementById('hero-next');

        function updateHeroSlider() {
            heroSlides.forEach((slide, index) => {
                slide.style.opacity = index === currentHeroSlide ? '1' : '0';
            });

            heroDots.forEach((dot, index) => {
                if (index === currentHeroSlide) {
                    dot.classList.add('bg-primary');
                    dot.classList.remove('bg-gray-300');
                } else {
                    dot.classList.remove('bg-primary');
                    dot.classList.add('bg-gray-300');
                }
            });
        }

        heroPrev?.addEventListener('click', () => {
            currentHeroSlide = currentHeroSlide === 0 ? heroSlides.length - 1 : currentHeroSlide - 1;
            updateHeroSlider();
        });

        heroNext?.addEventListener('click', () => {
            currentHeroSlide = currentHeroSlide === heroSlides.length - 1 ? 0 : currentHeroSlide + 1;
            updateHeroSlider();
        });

        heroDots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                currentHeroSlide = index;
                updateHeroSlider();
            });
        });

        // Auto-advance hero slider
        setInterval(() => {
            currentHeroSlide = currentHeroSlide === heroSlides.length - 1 ? 0 : currentHeroSlide + 1;
            updateHeroSlider();
        }, 7000);

        // Product filtering
        const filterBtns = document.querySelectorAll('.product-filter-btn');
        const productCards = document.querySelectorAll('.product-card');

        filterBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const filter = btn.dataset.filter;

                // Update active button
                filterBtns.forEach(b => {
                    b.classList.remove('active', 'bg-primary', 'text-white');
                    b.classList.add('text-gray-600');
                });
                btn.classList.add('active', 'bg-primary', 'text-white');
                btn.classList.remove('text-gray-600');

                // Filter products
                let visibleCount = 0;
                productCards.forEach(card => {
                    const category = card.dataset.category;
                    if (filter === 'all' || category === filter) {
                        card.style.display = 'block';
                        visibleCount++;
                    } else {
                        card.style.display = 'none';
                    }
                });

                document.getElementById('product-count').textContent = visibleCount;
            });
        });

        // Product interactions
        document.querySelectorAll('.wishlist-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const productCard = btn.closest('.product-card');
                const productId = Math.random().toString(36).substr(2, 9);
                const productName = productCard.querySelector('h4').textContent;

                toggleWishlist(productId, productName);

                const icon = btn.querySelector('i');
                icon.classList.toggle('fas');
                icon.classList.toggle('far');
                icon.classList.toggle('text-red-500');
            });
        });

        document.querySelectorAll('.add-to-cart-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const productCard = btn.closest('.product-card');
                const productId = Math.random().toString(36).substr(2, 9);
                const productName = productCard.querySelector('h4').textContent;
                const productPrice = productCard.dataset.price;

                addToCart(productId, productName, productPrice);
            });
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Navbar scroll effect
        window.addEventListener('scroll', () => {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 100) {
                navbar.classList.add('bg-white/98', 'shadow-2xl');
                navbar.classList.remove('bg-white/90');
            } else {
                navbar.classList.remove('bg-white/98', 'shadow-2xl');
                navbar.classList.add('bg-white/90');
            }
        });

        // Authentication Modal Functions
        window.openLoginModal = function() {
            const modal = document.getElementById('login-modal');
            const content = document.getElementById('login-modal-content');
            modal.classList.remove('hidden');
            setTimeout(() => {
                content.classList.remove('scale-95');
                content.classList.add('scale-100');
            }, 10);
            document.body.style.overflow = 'hidden';
        };

        window.openSignupModal = function() {
            const modal = document.getElementById('signup-modal');
            const content = document.getElementById('signup-modal-content');
            modal.classList.remove('hidden');
            setTimeout(() => {
                content.classList.remove('scale-95');
                content.classList.add('scale-100');
            }, 10);
            document.body.style.overflow = 'hidden';
        };

        window.closeAuthModal = function() {
            const loginModal = document.getElementById('login-modal');
            const signupModal = document.getElementById('signup-modal');
            const loginContent = document.getElementById('login-modal-content');
            const signupContent = document.getElementById('signup-modal-content');

            loginContent.classList.add('scale-95');
            signupContent.classList.add('scale-95');

            setTimeout(() => {
                loginModal.classList.add('hidden');
                signupModal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }, 300);
        };

        window.switchToSignup = function() {
            const loginModal = document.getElementById('login-modal');
            const signupModal = document.getElementById('signup-modal');
            const loginContent = document.getElementById('login-modal-content');
            const signupContent = document.getElementById('signup-modal-content');

            loginContent.classList.add('scale-95');
            setTimeout(() => {
                loginModal.classList.add('hidden');
                signupModal.classList.remove('hidden');
                setTimeout(() => {
                    signupContent.classList.remove('scale-95');
                    signupContent.classList.add('scale-100');
                }, 10);
            }, 150);
        };

        window.switchToLogin = function() {
            const loginModal = document.getElementById('login-modal');
            const signupModal = document.getElementById('signup-modal');
            const loginContent = document.getElementById('login-modal-content');
            const signupContent = document.getElementById('signup-modal-content');

            signupContent.classList.add('scale-95');
            setTimeout(() => {
                signupModal.classList.add('hidden');
                loginModal.classList.remove('hidden');
                setTimeout(() => {
                    loginContent.classList.remove('scale-95');
                    loginContent.classList.add('scale-100');
                }, 10);
            }, 150);
        };

        // Password visibility toggle
        window.togglePasswordVisibility = function(inputId) {
            const input = document.getElementById(inputId);
            const icon = document.getElementById(inputId + '-icon');

            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        };

        // Form validation
        function validateEmail(email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        }

        function checkPasswordStrength(password) {
            let strength = 0;
            const checks = [
                password.length >= 8,
                /[a-z]/.test(password),
                /[A-Z]/.test(password),
                /[0-9]/.test(password),
                /[^A-Za-z0-9]/.test(password)
            ];

            strength = checks.filter(Boolean).length;
            return strength;
        }

        // Real-time validation for signup form
        document.getElementById('signup-email')?.addEventListener('input', function() {
            const email = this.value;
            const validation = document.getElementById('email-validation');

            if (email && !validateEmail(email)) {
                validation.classList.remove('hidden');
                this.classList.add('border-red-500');
            } else {
                validation.classList.add('hidden');
                this.classList.remove('border-red-500');
            }
        });

        document.getElementById('signup-password')?.addEventListener('input', function() {
            const password = this.value;
            const strengthIndicator = document.getElementById('password-strength');
            const strengthText = document.getElementById('strength-text');

            if (password) {
                strengthIndicator.classList.remove('hidden');
                const strength = checkPasswordStrength(password);

                // Reset all strength indicators
                for (let i = 1; i <= 4; i++) {
                    const indicator = document.getElementById(`strength-${i}`);
                    indicator.classList.remove('bg-red-500', 'bg-yellow-500', 'bg-green-500');
                    indicator.classList.add('bg-gray-200');
                }

                // Update strength indicators
                const colors = ['bg-red-500', 'bg-red-500', 'bg-yellow-500', 'bg-green-500'];
                const texts = ['Very Weak', 'Weak', 'Fair', 'Strong'];

                for (let i = 1; i <= Math.min(strength, 4); i++) {
                    const indicator = document.getElementById(`strength-${i}`);
                    indicator.classList.remove('bg-gray-200');
                    indicator.classList.add(colors[strength - 1]);
                }

                strengthText.textContent = texts[Math.min(strength - 1, 3)] || 'Very Weak';
                strengthText.className = `text-sm ${strength >= 3 ? 'text-green-600' : strength >= 2 ? 'text-yellow-600' : 'text-red-600'}`;
            } else {
                strengthIndicator.classList.add('hidden');
            }
        });

        document.getElementById('signup-confirm-password')?.addEventListener('input', function() {
            const password = document.getElementById('signup-password').value;
            const confirmPassword = this.value;
            const matchIndicator = document.getElementById('password-match');

            if (confirmPassword && password !== confirmPassword) {
                matchIndicator.classList.remove('hidden');
                this.classList.add('border-red-500');
            } else {
                matchIndicator.classList.add('hidden');
                this.classList.remove('border-red-500');
            }
        });

        // Form submissions
        document.getElementById('login-form')?.addEventListener('submit', function(e) {
            e.preventDefault();
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;

            // Simulate login process
            showNotification('Logging in...', 'info');

            setTimeout(() => {
                showNotification('Login successful!', 'success');
                closeAuthModal();
                // Here you would typically redirect or update the UI
            }, 1500);
        });

        document.getElementById('signup-form')?.addEventListener('submit', function(e) {
            e.preventDefault();
            const name = document.getElementById('signup-name').value;
            const email = document.getElementById('signup-email').value;
            const password = document.getElementById('signup-password').value;
            const confirmPassword = document.getElementById('signup-confirm-password').value;
            const termsAccepted = document.getElementById('terms-checkbox').checked;

            // Validation
            if (!validateEmail(email)) {
                showNotification('Please enter a valid email address', 'error');
                return;
            }

            if (password !== confirmPassword) {
                showNotification('Passwords do not match', 'error');
                return;
            }

            if (checkPasswordStrength(password) < 2) {
                showNotification('Please choose a stronger password', 'error');
                return;
            }

            if (!termsAccepted) {
                showNotification('Please accept the terms of service', 'error');
                return;
            }

            // Simulate signup process
            showNotification('Creating account...', 'info');

            setTimeout(() => {
                showNotification('Account created successfully!', 'success');
                closeAuthModal();
                // Here you would typically redirect or update the UI
            }, 1500);
        });

        // Close modals when clicking outside
        document.getElementById('login-modal')?.addEventListener('click', function(e) {
            if (e.target === this) {
                closeAuthModal();
            }
        });

        document.getElementById('signup-modal')?.addEventListener('click', function(e) {
            if (e.target === this) {
                closeAuthModal();
            }
        });

        // Close modals with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeAuthModal();
            }
        });

        // Newsletter subscription
        const newsletterSection = document.querySelector('section input[type="email"]')?.closest('section');
        const emailInput = newsletterSection?.querySelector('input[type="email"]');
        const subscribeBtn = newsletterSection?.querySelector('button');

        subscribeBtn?.addEventListener('click', (e) => {
            e.preventDefault();
            const email = emailInput.value.trim();

            if (email && email.includes('@')) {
                subscribeBtn.textContent = 'Subscribed!';
                subscribeBtn.classList.add('bg-green-500');
                emailInput.value = '';

                setTimeout(() => {
                    subscribeBtn.textContent = 'Subscribe';
                    subscribeBtn.classList.remove('bg-green-500');
                }, 3000);
            } else {
                emailInput.classList.add('border-red-500');
                setTimeout(() => {
                    emailInput.classList.remove('border-red-500');
                }, 3000);
            }
        });

        // Scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in-up');
                }
            });
        }, observerOptions);

        document.querySelectorAll('section').forEach(section => {
            observer.observe(section);
        });

        // Add enhanced CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes float {
                0%, 100% { transform: translateY(0px); }
                50% { transform: translateY(-20px); }
            }

            @keyframes slideUp {
                from { transform: translateY(30px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }

            @keyframes fadeInUp {
                from { transform: translateY(30px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }

            .animate-fade-in-up {
                animation: fadeInUp 0.8s ease-out;
            }

            .product-filter-btn.active {
                background-color: #4B0082;
                color: white;
            }

            .product-filter-btn {
                color: #6B7280;
            }

            /* Enhanced Glassmorphism effects */
            .backdrop-blur-xl {
                backdrop-filter: blur(24px);
            }

            .backdrop-blur-md {
                backdrop-filter: blur(12px);
            }

            .backdrop-blur-sm {
                backdrop-filter: blur(4px);
            }

            /* Floating navbar styles */
            #navbar {
                margin: 0 16px;
                border-radius: 16px;
                box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            }

            /* Floating label styles */
            .peer:focus ~ label,
            .peer:valid ~ label {
                transform: translateY(-12px) scale(0.75);
                color: #4B0082;
            }

            .peer:placeholder-shown ~ label {
                transform: translateY(0) scale(1);
                color: #6B7280;
            }

            /* Modal animations */
            .modal-enter {
                animation: modalEnter 0.3s ease-out;
            }

            @keyframes modalEnter {
                from {
                    opacity: 0;
                    transform: scale(0.95) translateY(-10px);
                }
                to {
                    opacity: 1;
                    transform: scale(1) translateY(0);
                }
            }

            /* Loading skeleton */
            .skeleton {
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: loading 1.5s infinite;
            }

            @keyframes loading {
                0% { background-position: 200% 0; }
                100% { background-position: -200% 0; }
            }

            /* Enhanced hover effects */
            .group:hover .group-hover\\:scale-110 {
                transform: scale(1.1);
            }

            /* Button hover effects */
            .btn-hover {
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .btn-hover:hover {
                transform: translateY(-2px);
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            }

            /* Smooth transitions */
            * {
                transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
                transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
                transition-duration: 150ms;
            }

            /* Custom scrollbar */
            ::-webkit-scrollbar {
                width: 8px;
            }

            ::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 4px;
            }

            ::-webkit-scrollbar-thumb {
                background: linear-gradient(180deg, #4B0082, #9370DB);
                border-radius: 4px;
            }

            ::-webkit-scrollbar-thumb:hover {
                background: linear-gradient(180deg, #9370DB, #4B0082);
            }

            /* Form focus states */
            .form-input:focus {
                box-shadow: 0 0 0 3px rgba(75, 0, 130, 0.1);
                border-color: #4B0082;
            }

            /* Dropdown menu animations */
            .dropdown-enter {
                animation: dropdownEnter 0.2s ease-out;
            }

            @keyframes dropdownEnter {
                from {
                    opacity: 0;
                    transform: translateY(-10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            /* Mobile menu rounded corners */
            #mobile-menu {
                border-bottom-left-radius: 16px;
                border-bottom-right-radius: 16px;
            }
        `;
        document.head.appendChild(style);

        // Initialize everything
        updateCartUI();
        updateWishlistUI();
        updateHeroSlider();

        // Parallax effect for hero section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('#hero-section');
            if (parallax) {
                const speed = scrolled * 0.5;
                parallax.style.transform = `translateY(${speed}px)`;
            }
        });

        // Add loading states
        document.addEventListener('DOMContentLoaded', () => {
            // Simulate loading
            setTimeout(() => {
                document.body.classList.add('loaded');
            }, 500);
        });
        </script>
</body>
</html>