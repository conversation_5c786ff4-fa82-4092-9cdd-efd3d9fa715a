<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Luxe Fashion - Modern Clothing Store</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#4B0082',
                        'secondary': '#D8BFD8',
                        'accent': '#9370DB'
                    }
                }
            }
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-white text-gray-800">
    <!-- Navigation -->
    <nav class="bg-white shadow-md fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <h1 class="text-2xl font-bold text-primary">Luxe Fashion</h1>
                </div>

                <!-- Desktop Menu -->
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-8">
                        <a href="#" class="text-gray-700 hover:text-primary transition duration-300">Home</a>
                        <a href="#" class="text-gray-700 hover:text-primary transition duration-300">Women</a>
                        <a href="#" class="text-gray-700 hover:text-primary transition duration-300">Men</a>
                        <a href="#" class="text-gray-700 hover:text-primary transition duration-300">Accessories</a>
                        <a href="#" class="text-gray-700 hover:text-primary transition duration-300">Sale</a>
                    </div>
                </div>

                <!-- Icons -->
                <div class="flex items-center space-x-4">
                    <i class="fas fa-search text-gray-700 hover:text-primary cursor-pointer transition duration-300"></i>
                    <i class="fas fa-heart text-gray-700 hover:text-primary cursor-pointer transition duration-300"></i>
                    <i class="fas fa-shopping-bag text-gray-700 hover:text-primary cursor-pointer transition duration-300"></i>

                    <!-- Mobile menu button -->
                    <button id="mobile-menu-btn" class="md:hidden text-gray-700 hover:text-primary">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="#" class="block px-3 py-2 text-gray-700 hover:text-primary">Home</a>
                <a href="#" class="block px-3 py-2 text-gray-700 hover:text-primary">Women</a>
                <a href="#" class="block px-3 py-2 text-gray-700 hover:text-primary">Men</a>
                <a href="#" class="block px-3 py-2 text-gray-700 hover:text-primary">Accessories</a>
                <a href="#" class="block px-3 py-2 text-gray-700 hover:text-primary">Sale</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative h-screen flex items-center justify-center bg-gradient-to-r from-secondary to-white mt-16">
        <div class="absolute inset-0 bg-black opacity-10"></div>
        <div class="relative z-10 text-center px-4">
            <h2 class="text-5xl md:text-7xl font-bold text-primary mb-6">New Collection</h2>
            <p class="text-xl md:text-2xl text-gray-700 mb-8 max-w-2xl mx-auto">
                Discover the latest trends in fashion with our curated collection of premium clothing
            </p>
            <button class="bg-primary text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-accent transition duration-300 transform hover:scale-105">
                Shop Now
            </button>
        </div>

        <!-- Hero Image Placeholder -->
        <div class="absolute right-0 top-0 h-full w-1/2 hidden lg:block">
            <div class="h-full bg-gradient-to-l from-secondary to-transparent flex items-center justify-center">
                <div class="w-80 h-96 bg-primary opacity-20 rounded-lg"></div>
            </div>
        </div>
    </section>

    <!-- Featured Categories -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h3 class="text-3xl font-bold text-center text-primary mb-12">Shop by Category</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Women's Category -->
                <div class="group cursor-pointer transform hover:scale-105 transition duration-300">
                    <div class="relative h-80 bg-secondary rounded-lg overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-t from-primary to-transparent opacity-60"></div>
                        <div class="absolute bottom-6 left-6 text-white">
                            <h4 class="text-2xl font-bold mb-2">Women's</h4>
                            <p class="text-sm">Elegant & Trendy</p>
                        </div>
                    </div>
                </div>

                <!-- Men's Category -->
                <div class="group cursor-pointer transform hover:scale-105 transition duration-300">
                    <div class="relative h-80 bg-accent rounded-lg overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-t from-primary to-transparent opacity-60"></div>
                        <div class="absolute bottom-6 left-6 text-white">
                            <h4 class="text-2xl font-bold mb-2">Men's</h4>
                            <p class="text-sm">Classic & Modern</p>
                        </div>
                    </div>
                </div>

                <!-- Accessories Category -->
                <div class="group cursor-pointer transform hover:scale-105 transition duration-300">
                    <div class="relative h-80 bg-secondary rounded-lg overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-t from-primary to-transparent opacity-60"></div>
                        <div class="absolute bottom-6 left-6 text-white">
                            <h4 class="text-2xl font-bold mb-2">Accessories</h4>
                            <p class="text-sm">Complete Your Look</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center mb-12">
                <h3 class="text-3xl font-bold text-primary">Featured Products</h3>
                <div class="flex space-x-2">
                    <button id="prev-btn" class="p-2 rounded-full bg-secondary text-primary hover:bg-primary hover:text-white transition duration-300">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button id="next-btn" class="p-2 rounded-full bg-secondary text-primary hover:bg-primary hover:text-white transition duration-300">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>

            <div class="overflow-hidden">
                <div id="product-carousel" class="flex transition-transform duration-300 ease-in-out">
                    <!-- Product 1 -->
                    <div class="w-full md:w-1/2 lg:w-1/4 flex-shrink-0 px-4">
                        <div class="bg-white rounded-lg shadow-md overflow-hidden group">
                            <div class="h-64 bg-secondary relative">
                                <div class="absolute inset-0 bg-primary opacity-0 group-hover:opacity-20 transition duration-300"></div>
                                <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition duration-300">
                                    <i class="fas fa-heart text-primary cursor-pointer"></i>
                                </div>
                            </div>
                            <div class="p-4">
                                <h4 class="font-semibold text-gray-800 mb-2">Elegant Dress</h4>
                                <p class="text-primary font-bold">$89.99</p>
                            </div>
                        </div>
                    </div>

                    <!-- Product 2 -->
                    <div class="w-full md:w-1/2 lg:w-1/4 flex-shrink-0 px-4">
                        <div class="bg-white rounded-lg shadow-md overflow-hidden group">
                            <div class="h-64 bg-accent relative">
                                <div class="absolute inset-0 bg-primary opacity-0 group-hover:opacity-20 transition duration-300"></div>
                                <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition duration-300">
                                    <i class="fas fa-heart text-primary cursor-pointer"></i>
                                </div>
                            </div>
                            <div class="p-4">
                                <h4 class="font-semibold text-gray-800 mb-2">Casual Shirt</h4>
                                <p class="text-primary font-bold">$45.99</p>
                            </div>
                        </div>
                    </div>

                    <!-- Product 3 -->
                    <div class="w-full md:w-1/2 lg:w-1/4 flex-shrink-0 px-4">
                        <div class="bg-white rounded-lg shadow-md overflow-hidden group">
                            <div class="h-64 bg-secondary relative">
                                <div class="absolute inset-0 bg-primary opacity-0 group-hover:opacity-20 transition duration-300"></div>
                                <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition duration-300">
                                    <i class="fas fa-heart text-primary cursor-pointer"></i>
                                </div>
                            </div>
                            <div class="p-4">
                                <h4 class="font-semibold text-gray-800 mb-2">Designer Jeans</h4>
                                <p class="text-primary font-bold">$129.99</p>
                            </div>
                        </div>
                    </div>

                    <!-- Product 4 -->
                    <div class="w-full md:w-1/2 lg:w-1/4 flex-shrink-0 px-4">
                        <div class="bg-white rounded-lg shadow-md overflow-hidden group">
                            <div class="h-64 bg-accent relative">
                                <div class="absolute inset-0 bg-primary opacity-0 group-hover:opacity-20 transition duration-300"></div>
                                <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition duration-300">
                                    <i class="fas fa-heart text-primary cursor-pointer"></i>
                                </div>
                            </div>
                            <div class="p-4">
                                <h4 class="font-semibold text-gray-800 mb-2">Luxury Handbag</h4>
                                <p class="text-primary font-bold">$199.99</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-16 bg-gradient-to-r from-primary to-accent">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h3 class="text-3xl font-bold text-white mb-4">Stay in Style</h3>
            <p class="text-secondary mb-8 text-lg">Subscribe to our newsletter for exclusive offers and latest fashion trends</p>
            <div class="flex flex-col sm:flex-row max-w-md mx-auto">
                <input type="email" placeholder="Enter your email" class="flex-1 px-4 py-3 rounded-l-full sm:rounded-r-none rounded-r-full focus:outline-none focus:ring-2 focus:ring-secondary">
                <button class="bg-white text-primary px-6 py-3 rounded-r-full sm:rounded-l-none rounded-l-full font-semibold hover:bg-secondary transition duration-300 mt-2 sm:mt-0">
                    Subscribe
                </button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div>
                    <h4 class="text-xl font-bold text-secondary mb-4">Luxe Fashion</h4>
                    <p class="text-gray-400 mb-4">Your destination for premium fashion and timeless style.</p>
                    <div class="flex space-x-4">
                        <i class="fab fa-facebook text-secondary hover:text-white cursor-pointer transition duration-300"></i>
                        <i class="fab fa-instagram text-secondary hover:text-white cursor-pointer transition duration-300"></i>
                        <i class="fab fa-twitter text-secondary hover:text-white cursor-pointer transition duration-300"></i>
                        <i class="fab fa-pinterest text-secondary hover:text-white cursor-pointer transition duration-300"></i>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h5 class="font-semibold text-secondary mb-4">Quick Links</h5>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">About Us</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Contact</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Size Guide</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Returns</a></li>
                    </ul>
                </div>

                <!-- Categories -->
                <div>
                    <h5 class="font-semibold text-secondary mb-4">Categories</h5>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Women's Clothing</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Men's Clothing</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Accessories</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Sale Items</a></li>
                    </ul>
                </div>

                <!-- Customer Service -->
                <div>
                    <h5 class="font-semibold text-secondary mb-4">Customer Service</h5>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Help Center</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Shipping Info</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Track Order</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Privacy Policy</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-400">&copy; 2024 Luxe Fashion. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');

        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // Product carousel
        const carousel = document.getElementById('product-carousel');
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');
        let currentIndex = 0;
        const totalProducts = 4;

        function getProductsPerView() {
            return window.innerWidth >= 1024 ? 4 : window.innerWidth >= 768 ? 2 : 1;
        }

        function getMaxIndex() {
            return Math.max(0, totalProducts - getProductsPerView());
        }

        function updateCarousel() {
            const productsPerView = getProductsPerView();
            const translateX = -(currentIndex * (100 / productsPerView));
            carousel.style.transform = `translateX(${translateX}%)`;
        }

        prevBtn.addEventListener('click', () => {
            currentIndex = Math.max(0, currentIndex - 1);
            updateCarousel();
        });

        nextBtn.addEventListener('click', () => {
            currentIndex = Math.min(getMaxIndex(), currentIndex + 1);
            updateCarousel();
        });

        // Auto-scroll carousel
        setInterval(() => {
            const maxIndex = getMaxIndex();
            currentIndex = currentIndex >= maxIndex ? 0 : currentIndex + 1;
            updateCarousel();
        }, 5000);

        // Responsive carousel update
        window.addEventListener('resize', () => {
            currentIndex = Math.min(currentIndex, getMaxIndex());
            updateCarousel();
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Add scroll effect to navbar
        window.addEventListener('scroll', () => {
            const navbar = document.querySelector('nav');
            if (window.scrollY > 100) {
                navbar.classList.add('shadow-lg');
            } else {
                navbar.classList.remove('shadow-lg');
            }
        });

        // Add animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        }, observerOptions);

        // Observe all sections for scroll animations
        document.querySelectorAll('section').forEach(section => {
            observer.observe(section);
        });

        // Newsletter subscription
        const newsletterSection = document.querySelector('section input[type="email"]').closest('section');
        const emailInput = newsletterSection.querySelector('input[type="email"]');
        const subscribeBtn = newsletterSection.querySelector('button');

        subscribeBtn.addEventListener('click', (e) => {
            e.preventDefault();
            const email = emailInput.value.trim();

            if (email && email.includes('@')) {
                // Simulate subscription success
                subscribeBtn.textContent = 'Subscribed!';
                subscribeBtn.classList.add('bg-green-500');
                emailInput.value = '';

                setTimeout(() => {
                    subscribeBtn.textContent = 'Subscribe';
                    subscribeBtn.classList.remove('bg-green-500');
                }, 3000);
            } else {
                // Show error state
                emailInput.classList.add('border-red-500');
                setTimeout(() => {
                    emailInput.classList.remove('border-red-500');
                }, 3000);
            }
        });

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            .animate-fade-in {
                animation: fadeIn 0.8s ease-in-out;
            }

            @keyframes fadeIn {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            /* Hover effects */
            .group:hover .group-hover\\:scale-110 {
                transform: scale(1.1);
            }

            /* Loading animation for product placeholders */
            .bg-secondary, .bg-accent {
                background-image: linear-gradient(45deg, transparent 25%, rgba(255,255,255,0.1) 25%, rgba(255,255,255,0.1) 50%, transparent 50%, transparent 75%, rgba(255,255,255,0.1) 75%);
                background-size: 20px 20px;
                animation: shimmer 2s infinite linear;
            }

            @keyframes shimmer {
                0% { background-position: -20px 0; }
                100% { background-position: 20px 0; }
            }
        `;
        document.head.appendChild(style);

        // Initialize carousel on page load
        updateCarousel();
    </script>
</body>
</html>